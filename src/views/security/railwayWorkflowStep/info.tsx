import { DescItem } from '@/components/Description';
import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';

import { roleTypeDict, roleTypeMap, isKeyPointDict, isKeyPointMap } from './dict';

const { renderDict } = useRender();

export const descSchema: DescItem[] = [
  {
    label: '环节名称',
    field: 'stepName',
  },
  {
    label: '环节描述',
    field: 'description',
  },
  {
    label: '环节顺序',
    field: 'stepOrder',
  },
  {
    label: '角色类型',
    field: 'roleType',
    render: (value) => {
      return roleTypeMap[value] || value;
    },
  },
  {
    label: '附件类型',
    field: 'annexType',
    render: (value) => {
      return renderDict(value, DictEnum.STEP_ANNEX_TYPE);
    },
  },
  {
    label: '附件描述',
    field: 'annexDescription',
  },
  {
    label: '是否允许多个附件',
    field: 'isMultipleAttachments',
    render: (value) => {
      return renderDict(value, DictEnum.STEP_ANNEX_MULTIPLE_TYPE);
    },
  },
  {
    label: '识别项',
    field: 'detectionItem',
    render: (value) => {
      if (!value) return '';
      // 处理逗号分隔的多个值
      const values = value.split(',').filter(v => v.trim());
      return values.map(v => renderDict(v.trim(), DictEnum.STEP_DETECTION_ITEM)).join(', ');
    },
  },
  {
    label: '前置环节',
    field: 'preStepNames',
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
];
